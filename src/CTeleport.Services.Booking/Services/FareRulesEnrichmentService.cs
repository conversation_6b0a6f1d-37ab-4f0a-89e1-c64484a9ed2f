using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using CTeleport.Services.Booking.Models;
using CTeleport.Services.FareRules;
using CTeleport.Services.Search.Shared.Models;
using Serilog;

namespace CTeleport.Services.Booking.Services
{
    /// <summary>
    /// Service for enriching FareRules property in Reservation objects by calling the FareRules API
    /// </summary>
    public class FareRulesEnrichmentService : IFareRulesEnrichmentService
    {
        private readonly IFareRulesClient _fareRulesClient;
        private readonly ILogger _logger;

        public FareRulesEnrichmentService(IFareRulesClient fareRulesClient, ILogger logger)
        {
            _fareRulesClient = fareRulesClient ?? throw new ArgumentNullException(nameof(fareRulesClient));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Enriches the FareRules property of a reservation by fetching fare rule details from the FareRules service
        /// </summary>
        /// <param name="reservation">The reservation to enrich</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task representing the async operation</returns>
        public async Task EnrichFareRulesAsync(Reservation reservation, CancellationToken cancellationToken = default)
        {
            if (reservation == null)
            {
                _logger.Warning("Reservation is null, skipping FareRules enrichment");
                return;
            }

            if (reservation.FareRulesIds == null)
            {
                _logger.Information("No FareRulesIds found for reservation {ReservationId}, skipping enrichment", reservation.Id);
                return;
            }

            try
            {
                _logger.Information("Starting FareRules enrichment for reservation {ReservationId}", reservation.Id);

                // Collect all unique fare rule IDs from all O-D pairs
                var allFareRuleIds = reservation.FareRulesIds.Values
                    .SelectMany(ids => ids ?? new List<string>())
                    .Where(id => !string.IsNullOrEmpty(id))
                    .Distinct()
                    .ToList();

                if (!allFareRuleIds.Any())
                {
                    _logger.Information("No valid FareRule IDs found for reservation {ReservationId}", reservation.Id);
                    return;
                }

                _logger.Information("Requesting {Count} fare rules for reservation {ReservationId}", allFareRuleIds.Count, reservation.Id);

                // Call the FareRules API to get the fare rule sections
                var fareRuleSections = await _fareRulesClient.QueryFareRuleSectionsByIdsAsync(allFareRuleIds, cancellationToken);

                _logger.Information("Received {Count} fare rule sections from API for reservation {ReservationId}", 
                    fareRuleSections?.Count ?? 0, reservation.Id);

                if (fareRuleSections == null || !fareRuleSections.Any())
                {
                    _logger.Warning("No fare rule sections returned from API for reservation {ReservationId}", reservation.Id);
                    return;
                }

                // Create a lookup dictionary for quick access to fare rule sections by ID
                var fareRuleLookup = CreateFareRuleLookup(fareRuleSections, allFareRuleIds);

                // Initialize FareRules dictionary if it doesn't exist
                reservation.FareRules ??= new Dictionary<string, List<FareRuleSection>>();

                // Enrich FareRules for each O-D pair
                foreach (var fareRuleIdsPair in reservation.FareRulesIds)
                {
                    var odPair = fareRuleIdsPair.Key;
                    var fareRuleIds = fareRuleIdsPair.Value;

                    if (fareRuleIds == null || !fareRuleIds.Any())
                        continue;

                    var fareRulesForOdPair = new List<FareRuleSection>();

                    foreach (var fareRuleId in fareRuleIds)
                    {
                        if (fareRuleLookup.TryGetValue(fareRuleId, out var fareRuleSection))
                        {
                            fareRulesForOdPair.Add(fareRuleSection);
                        }
                        else
                        {
                            _logger.Warning("Fare rule section not found for ID {FareRuleId} in O-D pair {OdPair}", fareRuleId, odPair);
                        }
                    }

                    if (fareRulesForOdPair.Any())
                    {
                        reservation.FareRules[odPair] = fareRulesForOdPair;
                        _logger.Debug("Enriched {Count} fare rules for O-D pair {OdPair}", fareRulesForOdPair.Count, odPair);
                    }
                }

                _logger.Information("Successfully enriched FareRules for reservation {ReservationId} with {Count} O-D pairs", 
                    reservation.Id, reservation.FareRules.Count);
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error enriching FareRules for reservation {ReservationId}", reservation.Id);
                throw;
            }
        }

        /// <summary>
        /// Creates a lookup dictionary from fare rule sections
        /// </summary>
        private Dictionary<string, FareRuleSection> CreateFareRuleLookup(List<FareRules.Models.FareRuleSectionDto> fareRuleSections, List<string> requestedIds)
        {
            var lookup = new Dictionary<string, FareRuleSection>();

            // Map the returned sections to the requested IDs based on order
            // This assumes the API returns sections in the same order as requested
            for (int i = 0; i < Math.Min(fareRuleSections.Count, requestedIds.Count); i++)
            {
                var dto = fareRuleSections[i];
                var fareRuleId = requestedIds[i];

                var fareRuleSection = new FareRuleSection
                {
                    Category = dto.Category,
                    Title = dto.Title,
                    Text = dto.Text
                };

                lookup[fareRuleId] = fareRuleSection;
            }

            return lookup;
        }
    }
}
