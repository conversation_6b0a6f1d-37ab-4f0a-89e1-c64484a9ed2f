using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading;
using System.Threading.Tasks;
using CTeleport.Services.FareRules.Configuration;
using CTeleport.Services.FareRules.Models;
using CTeleport.Services.Helpers;
using Polly;
using Polly.Retry;
using RestEase;
using Serilog;

namespace CTeleport.Services.FareRules
{
    public class FareRulesClient : IFareRulesClient
    {
        private static readonly HttpStatusCode[] StopCodes = { HttpStatusCode.NotFound, HttpStatusCode.Forbidden, HttpStatusCode.InternalServerError };

        private readonly IFareRulesApi _api;
        private readonly AsyncRetryPolicy _policy;
        private readonly ILogger _log;

        public FareRulesClient(ILogger log, FareRulesApiOptions options)
        {
            _log = log ?? throw new ArgumentNullException(nameof(log));
            if (options == null) throw new ArgumentNullException(nameof(options));
            
            _api = RestClient.For<IFareRulesApi>(options.Url, HttpRequestHelper.AddApmTracingHeader);
            _policy = Policy
                .Handle<Exception>(e => !(e is ApiException apiException && StopCodes.Contains(apiException.StatusCode)))
                .WaitAndRetryAsync(3, retryAttempt => TimeSpan.FromMilliseconds(300 * retryAttempt), (exception, timeSpan, retryCount, context) =>
                    _log.Warning(exception, "Exception {ExceptionType} on {RetryCount} retry count", exception.GetType().Name, retryCount)
                );
        }

        public Task<List<FareRuleSectionDto>> QueryFareRuleSectionsByIdsAsync(List<string> ids, CancellationToken cancellationToken = default)
        {
            _log.Information("Requesting fare rules for {Count} IDs", ids?.Count ?? 0);
            return _policy.ExecuteAsync(() => _api.QueryFareRuleSectionsByIdsAsync(ids, cancellationToken));
        }
        
        [Header("Accept", "application/json")]
        public interface IFareRulesApi
        {
            [Post("farerules/sections/query")]
            Task<List<FareRuleSectionDto>> QueryFareRuleSectionsByIdsAsync([Body] List<string> ids, CancellationToken cancellationToken = default);
        }
    }
}
