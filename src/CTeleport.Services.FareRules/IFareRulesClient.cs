using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using CTeleport.FareRules.Shared.Dto;

namespace CTeleport.Services.FareRules
{
    public interface IFareRulesClient
    {
        /// <summary>
        /// Query fare rule sections by their IDs
        /// </summary>
        /// <param name="ids">List of fare rule IDs</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of fare rule sections</returns>
        Task<List<FareRuleSectionDto>> QueryFareRuleSectionsByIdsAsync(List<string> ids, CancellationToken cancellationToken = default);
    }
}
