using Autofac;
using CTeleport.Common.Extensions;
using CTeleport.Services.FareRules.Configuration;
using Microsoft.Extensions.Configuration;

namespace CTeleport.Services.FareRules
{
    public class FareRulesModule : Module
    {
        private readonly IConfiguration _configuration;

        public FareRulesModule(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        protected override void Load(ContainerBuilder builder)
        {
            builder.RegisterInstance(_configuration.GetSettings<FareRulesApiOptions>()).SingleInstance();

            builder.RegisterType<FareRulesClient>().As<IFareRulesClient>();
        }
    }
}
